# -*- coding: utf-8 -*-
"""
    time_helper
    ~~~~~~~

    时间工具类.

    :author: <PERSON>
    :copyright: (c) 2016, Tungee
    :date created: 2016-12-29
    :python version: 2.7
"""
import re
import time
from datetime import datetime, timedelta

from calendar import timegm, isleap, monthrange

from tdr.common.constant.common import CommonConstant
from time import mktime


def utc2local(utc_dt):
    """
    Convert utc datetime to local datetime
    :param utc_dt: utc 时间
    :return:
    """
    return datetime.fromtimestamp(timegm(utc_dt.timetuple()))


def datetime2timestamp(dt):
    """
    datetime 转 timestamp
    :param dt: datetime 时间
    :return:
    """
    return int(timegm(dt.timetuple()) * 1000 + dt.microsecond / 1e3)


def timestamp2datetime(stamp, to_local=False):
    """
    timestamp 转 datetime
    :param stamp: 时间戳
    :param to_local: 是否转本地时间
    :return:
    """
    dt = datetime.utcfromtimestamp(stamp / 1e3)
    if to_local:
        return utc2local(dt)
    return dt


def datetime2string(dt, date_format='%Y-%m-%d %H:%M:%S'):
    """
    datetime 转 str
    :param dt: datetime 时间
    :param date_format: 时间字符串格式
    :return:
    """
    return dt.strftime(date_format)


def local2utc(dt):
    """
    Convert local time string to utc datetime
    :param dt: 本地时间 转 UTC 时间
    :return:
    """
    return datetime.utcfromtimestamp(time.mktime(dt.timetuple()))


def string2datetime(date_str, date_format='%Y-%m-%d %H:%M:%S'):
    """
    字符串 转 datetime
    :param date_str: 时间字符串
    :param date_format: 时间格式
    :return:
    """
    return datetime.strptime(date_str, date_format)


def convert_arbitrary_date_format(date_str, formats=None):
    """
    字符串 转 datetime
    :param date_str: 时间字符串
    :param formats: 时间格式
    :return:
    """
    if not date_str:
        return None
    if formats is None:
        formats = [
            '%Y/%m',
            '%Y/%m/%d',
            '%Y-%m',
            '%Y-%m-%d',
            '%Y.%m',
            '%Y.%m.%d',
            '%m-%Y',
            '%b-%Y',
            '%d-%m-%Y',
            '%d-%b-%Y',
            '%Y-%m-%d %H:%M:%S',
        ]
    date = None
    for fmt in formats:
        try:
            date = datetime.strptime(date_str, fmt)
            break
        except ValueError:
            continue

    # if date is None:
    #     raise(ValueError, 'Invalid datetime string')
    if date is None:
        return None
    return date


def now_date_to_str():
    """
    当前UTC时间转字符串
    :return:
    """
    now = datetime.utcnow()
    return datetime2string(now)


def cal_found_time_limit_scale(dt_or_str):
    """
    计算时间范围 x<0<=y年以内
    :param dt_or_str: datetime类型或%Y-%m-%d格式字符串
    :return:
    """
    if isinstance(dt_or_str, str):
        match_1 = re.match('\d{4}-\d{1,2}-\d{1,2}', dt_or_str)
        match_2 = re.match('\d{4}-\d{1,2}', dt_or_str)
        match_3 = re.match('\d{4}', dt_or_str)
        try:
            if match_1:
                dt = datetime.strptime(dt_or_str, '%Y-%m-%d')
            elif match_2:
                dt = datetime.strptime(dt_or_str, '%Y-%m')
            elif match_3:
                dt = datetime.strptime(dt_or_str, '%Y')
            else:
                return None
        except:
            return None
    else:
        dt = dt_or_str

    now = datetime.now()
    today = datetime(now.year, now.month, now.day)

    def get_years_ago_date(date_begin, interval):
        y = date_begin.year - interval
        # 闰年2月29特殊处理
        if isleap(date_begin.year) and not isleap(y) \
                and date_begin.month == 2 and date_begin.day == 29:
            return datetime(y, date_begin.month, 28)
        return datetime(y, date_begin.month, date_begin.day)

    if dt >= get_years_ago_date(today, 1):
        return u'成立1年内'
    if get_years_ago_date(today, 3) <= dt < get_years_ago_date(today, 1):
        return u'成立3年内'
    if get_years_ago_date(today, 5) <= dt < get_years_ago_date(today, 3):
        return u'成立5年内'
    if get_years_ago_date(today, 10) <= dt < get_years_ago_date(today, 5):
        return u'成立10内年'
    if get_years_ago_date(today, 15) <= dt < get_years_ago_date(today, 10):
        return u'成立15内年'
    return u'成立15年以上'


def cal_found_time_big_or_small(dt_or_str, anchor_dt_or_str):
    """
    比较时间大小
    dt_or_str： 待比较时间点，
    anchor_dt_or_str：锚定时间点
    :param dt_or_str: datetime类型或%Y-%m-%d格式字符串
    :return: 大于锚定时间点：True 小于锚定时间点：False
    """
    if isinstance(dt_or_str, str):
        match_1 = re.match('\d{4}-\d{1,2}-\d{1,2}', dt_or_str)
        match_2 = re.match('\d{4}-\d{1,2}', dt_or_str)
        match_3 = re.match('\d{4}', dt_or_str)
        try:
            if match_1:
                dt = datetime.strptime(dt_or_str, '%Y-%m-%d')
            elif match_2:
                dt = datetime.strptime(dt_or_str, '%Y-%m')
            elif match_3:
                dt = datetime.strptime(dt_or_str, '%Y')
            else:
                return None
        except:
            return None
    else:
        dt = dt_or_str

    if isinstance(anchor_dt_or_str, str):
        match_4 = re.match('\d{4}-\d{1,2}-\d{1,2}', anchor_dt_or_str)
        match_5 = re.match('\d{4}-\d{1,2}', anchor_dt_or_str)
        match_6 = re.match('\d{4}', anchor_dt_or_str)
        try:
            if match_4:
                anchor_dt = datetime.strptime(anchor_dt_or_str, '%Y-%m-%d')
            elif match_5:
                anchor_dt = datetime.strptime(anchor_dt_or_str, '%Y-%m')
            elif match_6:
                anchor_dt = datetime.strptime(anchor_dt_or_str, '%Y')
            else:
                return None
        except:
            return None
    else:
        anchor_dt = anchor_dt_or_str

    if dt >= anchor_dt:
        return True
    else:
        return False


def cal_found_time_duration(dt_or_str):
    """
    计算时间范围 x<=0<y年以内
    :param dt_or_str: datetime类型或%Y-%m-%d格式字符串
    :return:
    """
    if isinstance(dt_or_str, str):
        match_1 = re.match('\d{4}-\d{1,2}-\d{1,2}', dt_or_str)
        match_2 = re.match('\d{4}-\d{1,2}', dt_or_str)
        match_3 = re.match('\d{4}', dt_or_str)
        try:
            if match_1:
                dt = datetime.strptime(dt_or_str, '%Y-%m-%d')
            elif match_2:
                dt = datetime.strptime(dt_or_str, '%Y-%m')
            elif match_3:
                dt = datetime.strptime(dt_or_str, '%Y')
            else:
                return None
        except:
            return None
    else:
        dt = dt_or_str

    now = datetime.now()
    today = datetime(now.year, now.month, now.day)

    def get_years_ago_date(date_begin, interval):
        y = date_begin.year - interval
        # 闰年2月29特殊处理
        if isleap(date_begin.year) and not isleap(y) \
                and date_begin.month == 2 and date_begin.day == 29:
            return datetime(y, date_begin.month, 28)
        return datetime(y, date_begin.month, date_begin.day)

    if dt > get_years_ago_date(today, 1):
        return u'成立不足1年'
    if get_years_ago_date(today, 3) < dt <= get_years_ago_date(today, 1):
        return u'成立1-3年'
    if get_years_ago_date(today, 5) < dt <= get_years_ago_date(today, 3):
        return u'成立3-5年'
    if get_years_ago_date(today, 10) < dt <= get_years_ago_date(today, 5):
        return u'成立5-10年'
    if get_years_ago_date(today, 15) < dt <= get_years_ago_date(today, 10):
        return u'成立10-15年'
    return u'成立15年以上'


def cal_time_range(dt):
    """
    计算时长(运营系统使用)
    :param dt: datetime
    :return:
    """
    total_days = (datetime.utcnow() - dt).days
    years = total_days / 365
    months = total_days % 365 / 30
    days = (total_days % 365) % 30
    if years > 0:
        if months > 0:
            if days > 0:
                time_range = u'%s年%s个月%s天' % (years, months, days)
            else:
                time_range = u'%s年%s个月' % (years, months)
        else:
            if days > 0:
                time_range = u'%s年%s天' % (years, days)
            else:
                time_range = u'%s年' % years
    else:
        if months > 0:
            if days > 0:
                time_range = u'%s个月%s天' % (months, days)
            else:
                time_range = u'%s个月' % months
        else:
            if days > 0:
                time_range = u'%s天' % days
            else:
                time_range = u'不足1天'

    return time_range


def cal_domain_age(dt_or_str):
    """
    计算域名年龄
    :param dt_or_str: datetime 或 str 时间
    :return:
    """
    if isinstance(dt_or_str, str):
        dt_or_str = dt_or_str.encode('utf-8')
        match_1 = re.match('\d{4}-\d{1,2}-\d{1,2}', dt_or_str)
        match_2 = re.match('\d{4}-\d{1,2}', dt_or_str)
        match_3 = re.match('\d{4}', dt_or_str)
        try:
            if match_1:
                dt = datetime.strptime(dt_or_str, '%Y-%m-%d')
            elif match_2:
                dt = datetime.strptime(dt_or_str, '%Y-%m')
            elif match_3:
                dt = datetime.strptime(dt_or_str, '%Y')
            else:
                return None
        except:
            return None
    else:
        dt = dt_or_str

    now = datetime.now()

    result_days = now.day - dt.day
    day_tmp = 0
    if result_days < 0:
        if dt.month == 2 and dt.year % 4 != 0:
            result_days += 28
        elif dt.month == 2 and dt.year % 4 == 0:
            result_days += 29
        elif dt.month in [1, 3, 5, 7, 8, 10, 12]:
            result_days += 31
        else:
            result_days += 30
        day_tmp = 1
    result_months = now.month - dt.month - day_tmp
    month_tmp = 0
    if result_months < 0:
        result_months += 12
        month_tmp = 1
    result_years = now.year - dt.year - month_tmp

    years = result_years
    months = result_months
    days = result_days
    if years > 0:
        if months > 0:
            if days > 0:
                time_range = u'%s年%s个月%s天' % (years, months, days)
            else:
                time_range = u'%s年%s个月' % (years, months)
        else:
            if days > 0:
                time_range = u'%s年%s天' % (years, days)
            else:
                time_range = u'%s年' % years
    else:
        if months > 0:
            if days > 0:
                time_range = u'%s个月%s天' % (months, days)
            else:
                time_range = u'%s个月' % months
        else:
            if days > 0:
                time_range = u'%s天' % days
            else:
                time_range = u'不足1天'

    return time_range


def time_add_months(time_begin, months):
    """
    添加月份
    :param time_begin: 基准时间
    :param months: 月数
    :return:
    """
    target_year = time_begin.year
    target_month = time_begin.month
    target_year += months / 12
    target_month += months % 12
    if target_month > 12:
        target_month -= 12
        target_year += 1

    # 计算目标月份的最大月份
    begin_max_day = monthrange(time_begin.year, time_begin.month)[1]
    target_max_day = monthrange(target_year, target_month)[1]

    if time_begin.day == begin_max_day:
        # 如果当前天数是当月的最大天数，则目标月份使用最大天数
        target_day = target_max_day
    else:
        target_day = min(time_begin.day, target_max_day)

    return datetime(target_year, target_month, target_day)


def time_add_years(years, time_now=None):
    """
    添加年份
    :param years: 年数
    :param time_now: 基准时间
    :return:
    """
    if time_now is None:
        time_now = datetime.now()
    return time_add_months(time_now, 12 * years)


def get_next_month_same_day(day, date_begin=None):
    """
    获取下个月同一天(如果没有同一天，则获取下下个余额的第一天)
    :param day:
    :param date_begin: 基准时间
    :return:
    """
    if date_begin is not None:
        now = date_begin
    else:
        now = datetime.now()
        now = datetime(now.year, now.month, now.day)

    # 获取下个月
    next_year = now.year
    next_month = now.month + 1
    if next_month > 12:
        next_year += 1
        next_month -= 12

    next_month_days = monthrange(next_year, next_month)[1]

    if day > next_month_days:
        date = datetime(next_year, next_month, next_month_days) + timedelta(1)
    else:
        date = datetime(next_year, next_month, day)

    return date


def get_date_list(date_begin, date_end, fmt='%Y-%m-%d'):
    """
    获取日期列表
    :param date_begin: 起始时间
    :param date_end: 结束时间
    :param fmt: 格式
    :return:
    """
    time_begin = datetime.strptime(date_begin, fmt)
    time_end = datetime.strptime(date_end, fmt)

    time_tmp = time_begin
    date_list = list()
    while time_tmp <= time_end:
        date_list.append(time_tmp.strftime(fmt))
        time_tmp += timedelta(days=1)

    return date_list


def recycle_time_countdown(rt):
    """
    回收时间倒计时
    :param rt: 当前时间
    :return:
    """
    now = datetime.utcnow()
    if rt < datetime.utcnow():
        rt = now
    delta = rt - now
    days = int(delta.total_seconds() / (3600 * 24))
    hours = int((delta.total_seconds() % (3600 * 24)) / 3600)
    if days > 0:
        rt_str = '%s天' % days
    elif hours > 0:
        rt_str = '%s小时' % hours
    else:
        rt_str = '1小时'

    return rt_str


def get_month_begin_and_and_dates(dt):
    """
    获取月份的开始和结束日期
    :param dt:时间日期
    """
    month_max_day = monthrange(dt.year, dt.month)[1]
    begin_date = datetime(dt.year, dt.month, 1)
    end_date = datetime(dt.year, dt.month, month_max_day)

    return begin_date, end_date


def dt2str(dt):
    """
    时间转为字符串
    :param dt:时间日期
    """
    dt_str = '{0.year:4d}年{0.month:02d}月{0.day:02d}日'.format(dt)
    return dt_str


def timestamp2str(timestamp, time_format=CommonConstant.TIME_FORMAT):
    """
    时间戳转字符串格式
    :param timestamp: 时间戳
    :param format: 时间格式
    :return:
    """
    return time.strftime(time_format, time.localtime(timestamp))


def datetime_encode(dt):
    """
    datetime 转 字符串
    :param dt: 时间
    :return:
    """
    # fix python3
    # dt.strftime(CommonConstant.TIME_FORMAT)
    return '{0.year:04d}-{0.month:02d}-{0.day:02d} {0.hour:02d}:{0.minute:02d}:{0.second:02d}'.format(dt)


def datetime_encode_only_date(dt):
    """
    datetime 转 字符串（只包括年月日）
    :param dt: 时间
    :return:
    """
    # fix python3
    # dt.strftime(CommonConstant.TIME_FORMAT_ONLY_DATE)
    return '{0.year:04d}-{0.month:02d}-{0.day:02d}'.format(dt)


def datetime2sec(dt):
    """
    datetime 转 秒数
    :param dt: 时间
    :return:
    """
    return time.mktime(dt.timetuple())


def sec2datetime(sec):
    """
    秒数 转 datetime
    :param sec:
    :return:
    """
    return datetime.fromtimestamp(sec)


def timestamp2datetime(stamp):
    """
    将时间戳转换成为 datetime
    :param stamp: 时间戳
    :return:
    """
    dt = datetime.fromtimestamp(stamp)
    return dt


def get_n_year_sec_from_now(n):
    """
    获取n年今天的时间戳
    :param n: 年数
    :return:
    """
    return datetime2sec(datetime.now()) - n * 365 * 24 * 60 * 60


def convert_chinese_arbitrary_date_format(date_str, dst_fmt=None):
    """
    使用time进行时间转换,避免有些环境无法使用datetime模块进行转换的可能
    :param date_str: 时间字符串
    :param dst_fmt: 时间格式
    :return:
    """
    formats = [
        u'%Y年%m月%d日',
        u'%Y年%m月%d',
        u'%Y年%m月',
        u'%Y年%m',
        u'%Y年',
        '%Y'
    ]
    date = None
    for fmt in formats:
        try:
            date = time.strptime(date_str, fmt)
            break
        except ValueError:
            continue

    if date is None:
        return None
    try:
        date = datetime.fromtimestamp(mktime(date))
    except ValueError:
        return None
    if dst_fmt:
        date = date.strftime(dst_fmt)
    return date


ch_dict = {u"零": '0', u"一": '1', u"二": '2', u"三": '3', u"四": '4', u"五": '5', u"六": '6', u"七": '7', u"八": '8',
           u"九": '9',
           u"十": '10'}


def chinese_date_num_to_num(chinese_num):
    """
    将时间字符串中含有大写的字母全部替换成阿拉伯数字,年月日进行保留
    :param chinese_num: 中文数字
    :return:
    """
    num = []
    for i in chinese_num:
        if not ch_dict.get(i):
            num.append(i)
        else:
            num.append(ch_dict.get(i))
    return "".join(num)


def convert_date_format(date_str, dst_fmt=None):
    """
    对提取出来的时间进行格式统一
    :param date_str: '%Y/%m',
        '%Y/%m/%d',
        '%Y-%m',
        '%Y-%m-%d',
        '%Y.%m',
        '%Y.%m.%d',
        '%m-%Y',
        '%b-%Y',
        '%d-%m-%Y',
        '%d-%b-%Y',
        '%Y-%m-%d %H:%M:%S',
        '%Y年%m月%d日',
        '%Y年%m月%d',
        '%Y年%m月',
        '%Y年%m',
        '%Y年',
        '%Y'
    :param dst_fmt: 默认不给,输出datetime类型的时间，如给出如date_str的格式,则输出对应格式的字符串
    :return:
    """
    if u'一' in date_str or u'二' in date_str or u'三' in date_str:
        # 保证一九九九,二零零一,三零零一都能被处理
        date_str = chinese_date_num_to_num(date_str)
    convert_date = convert_arbitrary_date_format(date_str, dst_fmt)
    if convert_date:
        return convert_date
    else:
        return convert_chinese_arbitrary_date_format(date_str, dst_fmt)


def date_diff(date1, date2):
    """
    计算两个日期之间相隔天数
    :param date1: 日期1
    :param date2: 日期2
    :return:
    """
    if not re.compile(r'^\d{4}-\d{2}-\d{2}$').match(date1):
        return None
    if not re.compile(r'^\d{4}-\d{2}-\d{2}$').match(date2):
        return None
    time_array1 = time.strptime(date1, "%Y-%m-%d")
    timestamp_day1 = int(time.mktime(time_array1))
    time_array2 = time.strptime(date2, "%Y-%m-%d")
    timestamp_day2 = int(time.mktime(time_array2))
    days = (timestamp_day2 - timestamp_day1) // 60 // 60 // 24
    return days


def is_leap_year(year):
    """
    年份是否是闰年
    :param year: 年份
    :return: bool，True: 闰年; False: 非闰年
    """
    return (year % 4 == 0 and year % 100 != 0) or year % 400 == 0


def parse_to_datetime(time_attr):
    """
    转换为datetime对象，兼容各种日期格式(int，float，str，dict)
    :param time_attr:
    :return:
    """
    default_time = datetime(1970, 1, 1, 0, 0, 0)
    try:
        if not time_attr:
            return default_time
        elif isinstance(time_attr, int) or isinstance(time_attr, float):
            return datetime.fromtimestamp(time_attr)
        elif isinstance(time_attr, str):
            return parse_str_to_datetime(time_attr)
        elif isinstance(time_attr, datetime):
            return time_attr
        elif isinstance(time_attr, dict):
            val = time_attr.get('val')
            if val:
                return val if isinstance(val, datetime) else parse_str_to_datetime(val)
            val = time_attr.get('$date')
            if val:
                return val if isinstance(val, datetime) else parse_str_to_datetime(val)
            return default_time
        else:
            return default_time
    except:
        return default_time


def parse_to_datetime_bidding(time_attr):
    """
    转换为datetime对象，兼容各种日期格式(int，float，str，dict)
    :param time_attr:
    :return:
    """
    default_time = datetime(1970, 1, 1, 0, 0, 0)
    try:
        if not time_attr:
            return default_time
        elif isinstance(time_attr, int) or isinstance(time_attr, float):
            return datetime.fromtimestamp(time_attr)
        elif isinstance(time_attr, str):
            return parse_str_to_datetime_bidding(time_attr)
        elif isinstance(time_attr, datetime):
            return time_attr
        elif isinstance(time_attr, dict):
            val = time_attr.get('val')
            if val:
                return val if isinstance(val, datetime) else parse_str_to_datetime_bidding(val)
            val = time_attr.get('$date')
            if val:
                return val if isinstance(val, datetime) else parse_str_to_datetime_bidding(val)
            return default_time
        else:
            return default_time
    except:
        return default_time


def parse_str_to_datetime(time_str):
    """
    转换字符串为时间对象
    :param time_str: 时间字符串
    :return:
    """
    if 'T' in time_str:
        if '.' in time_str:
            time_str = time_str[0:time_str.index('.')] + 'Z'
        return datetime.strptime(time_str, '%Y-%m-%dT%H:%M:%SZ')
    elif ' ' in time_str:
        return datetime.strptime(time_str, '%Y-%m-%d %H:%M:%S')
    elif '-' in time_str:
        return datetime.strptime(time_str, '%Y-%m-%d')
    else:
        return datetime(1970, 1, 1, 0, 0, 0)


def parse_str_to_datetime_bidding(time_str):
    """
    转换字符串为时间对象
    :param time_str: 时间字符串
    :return:
    """
    if 'T' in time_str:
        if '.' in time_str:
            time_str = time_str[0:time_str.index('.')] + 'Z'
        return datetime.strptime(time_str, '%Y-%m-%dT%H:%M:%SZ')
    elif ' ' in time_str:
        return datetime.strptime(time_str, '%Y-%m-%d %H:%M:%S')
    elif '-' in time_str:
        # 这里对创建时间需求做的特殊处理，其他地方使用注意
        time_str = time_str + " " + "23:59:59"
        return datetime.strptime(time_str, '%Y-%m-%d %H:%M:%S')
    else:
        return datetime(1970, 1, 1, 0, 0, 0)


def time_in_range(time_inst, start_time=None, end_time=None):
    """
    判断一个时间是否在指定范围内
    :param time_inst: 时间
    :param start_time: 起始时间
    :param end_time: 终止时间
    :return:
    """
    if not time_inst:
        return False
    if not start_time and not end_time:
        return False

    if start_time and time_inst < start_time:
        return False
    if end_time and time_inst > end_time:
        return False
    return True
