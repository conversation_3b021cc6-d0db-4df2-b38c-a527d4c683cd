#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    :File: spark_utils
    :author: <PERSON><PERSON><PERSON>
    :created: 2022/7/12 10:20
    :copyright: (c) 2021, Tungee
    :python version: 3
    :description: 封装Spark常用方法
"""

from pyspark import SparkConf, SparkContext, RDD
from pyspark.sql import SparkSession, DataFrame

from tdr.common.utils.json_util import loads_data, dumps_json


def create_spark_context(
        app_name: str,
        master: str = None,
        config: dict = None
) -> SparkContext:
    """
    create `SparkContext` object
    @param app_name: Set application name.
    @param master: Sets the Spark master URL to connect to
    @param config: spark配置
    @return:
    """
    conf = SparkConf().setAppName(app_name)
    if master:
        conf.setMaster(master)
    if config:
        for key, val in config.items():
            conf.set(key, val)
    spark_context = SparkContext.getOrCreate(conf=conf)
    return spark_context


def create_spark_session(
        sc: SparkContext = None,
        app_name: str = None,
        master: str = None,
        conf: SparkConf = None
) -> SparkSession:
    """
    create `SparkSession` object
    @param sc: SparkContext上下文
    @param app_name: Set application name.
    @param master: Sets the Spark master URL to connect to
    @param conf: SparkConf配置
    @return:
    """
    # 通过SparkContext对象创建
    if sc:
        session = SparkSession(sparkContext=sc)
        return session

    assert app_name is not None, 'appName must be set'
    # 通过Builder对象创建
    _builder = SparkSession.builder
    _builder.appName(app_name)
    if master:
        _builder.master(master=master)
    if conf:
        _builder.config(conf=conf)
    return _builder.getOrCreate()


def activate_pymongo_spark():
    """
    import `pymongo_spark` module
    @return:
    """
    import pymongo_spark
    pymongo_spark.activate()


def get_rdd_from_text_file(sc: SparkContext, path: str):
    """
    read normal text file
    @param sc: SparkContext上下文
    @param path: 文件路径
    @return:
    """
    return sc.textFile(path)


def get_rdd_from_json_file(sc: SparkContext, path: str) -> RDD:
    """
    read json file
    @param sc: SparkContext上下文
    @param path: 文件路径
    @return:
    """
    return sc.textFile(path).map(loads_data)


def get_rdd_from_bson_file(sc: SparkContext, path: str):
    """
    read bson file
    @param sc: SparkContext上下文
    @param path: 文件路径
    @return:
    """
    activate_pymongo_spark()
    return sc.BSONFileRDD(path)


def get_rdd_from_file(sc: SparkContext, path):
    """
    generate rdd from file
    @param sc: SparkContext上下文
    @param path: 文件路径
    @return:
    """
    if path.endswith('bson'):
        get_rdd_func = get_rdd_from_bson_file
    else:
        get_rdd_func = get_rdd_from_json_file
    rdd = get_rdd_func(sc=sc, path=path)
    return rdd


def rdd_union(sc: SparkContext, *rdds: RDD):
    """
    union rdd
    @param sc: SparkContext上下文
    @param rdds: rdd列表
    @return:
    """
    return sc.union(rdds)


def get_rdd_from_files(sc: SparkContext, *paths):
    """generate rdd from multi-file"""
    rdds = []
    for path in paths:
        rdd = get_rdd_from_file(sc, path)
        rdds.append(rdd)
    return rdd_union(sc, *rdds)


def rdd_union_from_file(sc: SparkContext, *paths):
    """
    union rdd from file
    @param sc: SparkContext上下文
    @param paths: 文件路径
    @return:
    """
    rdds = []
    for path in paths:
        rdd = get_rdd_from_file(sc, path)
        rdds.append(rdd)
    return rdd_union(sc, rdds)


def rdd_inner_join(rdd_l, key_getter_l, rdd_r, key_getter_r):
    """
    inner join for rdd
    @param rdd_l: 左rdd
    @param key_getter_l: 左key生成函数
    @param rdd_r: 右rdd
    @param key_getter_r: 右key生成函数
    @return:
    """
    return rdd_l.map(
        lambda doc: (key_getter_l(doc), doc)
    ).join(
        rdd_r.map(lambda doc: (key_getter_r(doc), doc))
    )


def rdd_left_outer_join(rdd_l, key_getter_l, rdd_r, key_getter_r) -> RDD:
    """
    left outer join for rdd
    @param rdd_l: 左rdd
    @param key_getter_l: 左key生成函数
    @param rdd_r: 右rdd
    @param key_getter_r: 右key生成函数
    @return:
    """
    return rdd_l.map(
        lambda doc: (key_getter_l(doc), doc)
    ).leftOuterJoin(
        rdd_r.map(lambda doc: (key_getter_r(doc), doc))
    )


def rdd_outer_join(rdd_l, key_getter_l, rdd_r, key_getter_r):
    """
    full outer join for rdd
    @param rdd_l: 左rdd
    @param key_getter_l: 左key生成函数
    @param rdd_r: 右rdd
    @param key_getter_r: 右key生成函数
    @return:
    """
    return rdd_l.map(
        lambda doc: (key_getter_l(doc), doc)
    ).fullOuterJoin(
        rdd_r.map(lambda doc: (key_getter_r(doc), doc))
    )


def rdd_to_df(rdd, schema=None) -> DataFrame:
    """
    RDD convert to DataFrame
    @param rdd: rdd
    @param schema: DataFrame结构
    @return:
    """
    df = rdd.toDF(schema)
    return df


def df_join(df_l: DataFrame, df_r: DataFrame, on, how):
    """
    join for dataframe
    @param df_l: 左表
    @param df_r: 右表
    @param on: 关联条件
    @param how: 关联方式
    @return:
    """
    return df_l.join(df_r, on=on, how=how)


def persist_rdd(
        rdd: RDD, path: str,
        formatFunc=dumps_json,
        numPartitions=None,
        shuffle=True,
        compressionCodecClass=None
):
    """
    持久化输出RDD
    @param rdd: rdd
    @param path: 输出路径
    @param formatFunc: 格式化函数，默认转化为json字符串
    @param numPartitions: 重分区数
    @param shuffle: 是否shuffle
    @param compressionCodecClass: 压缩编解码器类的完全限定类名，默认为None
    @return:
    """
    if callable(formatFunc):
        rdd = rdd.map(formatFunc)
    if numPartitions:
        rdd = rdd.coalesce(numPartitions, shuffle)
    rdd.saveAsTextFile(path, compressionCodecClass)
