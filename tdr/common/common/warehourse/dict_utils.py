# -*- coding: utf-8 -*-
"""
    dict_utils
    ~~~~~~~
    
    dict 工具类
    
    :author: <PERSON>
    :copyright: (c) 2022, Tungee
    :date created: 2022-10-21
    :python version: 
"""


def drop_none(d):
    """
    将一个 dict 中的值为 None 的字段 pop 掉
    :param d: dict 对象
    :return:
    """
    if d is None:
        return None
    elif isinstance(d, list):
        return list(filter(lambda x: x is not None, map(drop_none, d)))
    elif not isinstance(d, dict):
        return d
    else:
        r = dict(
            filter(lambda x: x[1] is not None,
                   map(lambda x: (x[0], drop_none(x[1])),
                       d.items())))
        if not bool(r):
            return None
        return r


def merge_tables(x_data, y_data):
    x_data.update(y_data)
    return x_data


def contain_fields(doc, fields):
    """
    判断数据中是否包含所有的字段
    fields为一个list, 里面的元素可以为字符串或者list
    1. 字符串表示数据中必须包含该字段
    2. list表示数据中必须包含list中字段的一个
    example1: fields = [field1, field2, field3]
    那么doc中必须包含field1, field2, field3三个字段才视为有效数据
    example2: fields = [field1, field2, [field3, field4]]
    那么doc中必须包含field1, field2, 以及至少包含field3和field4中的一个才视为有效数据
    :param doc: dict 数据
    :param fields: 字段
    :return: bool， True：存在字段有效值；False: 不存在字段或为空
    """
    for field in fields:
        if isinstance(field, str):
            # 不存在字段或者字段为空都视为不存在
            if field not in doc or doc.get(field) is None:
                return False
        elif isinstance(field, list):
            if not any(f in doc and doc.get(f) is not None for f in field):
                return False
    return True