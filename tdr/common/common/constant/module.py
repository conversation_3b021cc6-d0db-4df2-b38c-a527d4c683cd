#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    ~~~~~~~~~~~~~~~~~~~~~~~

    项目模块常量

    :author:  zhangping
    :copyright: (c) 2021, Tungee
    :date created: 2021-07-20
    :python version: 3.5


"""


class ModuleNameConstant(object):
    """项目模块常量"""
    bidding = "bidding"
    fair = "fair"
    enforcement = 'enforcement'  # 执行信息
    enterprise = 'enterprise'  # 企业信息
    contact = 'contact'  # 企业信息
    npo = "npo"
    news = "news"
    law_firm = "law_firm"
    law = "law"
    human = "human"
    fair_exhibitor = "fair_exhibitor"  # 参展商
    fair_mapping = "fair_mapping"  # 展会参展商 映射
    pavilion = "pavilion"
    general_label = "general_label"  # 通用版标签计算
    industry = "industry"  # 找企业【低代码】

    # 找网店
    eshop = 'eshop'

    # 找地产
    estate = "estate"

    proposed_project = "proposed_project"

    # 找渠道
    channel = 'channel'

    # 找工厂
    factory = 'factory'

    # 找店铺
    oo_store = 'oo_store'

    # 市场洞察
    market_insight = 'market_insight'

    # 战略新兴产业
    strategic_emerging_industry = 'strategic_emerging_industry'

    # 产业链
    industry_chain = "industry_chain"

    # 智慧服务
    intelligent_service = 'intelligent_service'

    # 产业招商
    industrial_merchants = 'industrial_merchants'

    # 金融版
    bank = 'bank'

    # 掘金
    finance_enterprise = "finance_enterprise"

    # 找客易
    easy_leads = 'easy_leads'

    # 招投标官网版
    engineer = 'engineer'

    # 知识产权行业版
    intellectual_property = 'intellectual_property'

    # PaaS
    paas = 'paas'

    # 资质证书服务行业版
    qualification_agent = 'qualification_agent'

    # 数据市场
    marketplace = 'marketplace'

    # 国际物流行业版
    logistics = 'logistics'

    # 制造业行业版
    manufacturing = 'manufacturing'

    # 探迹数据引擎(tungee-data-engine)
    tde = 'tde'

    # 国际外贸行业版
    foreigntrade = 'foreigntrade'

    # 工程版
    project = 'project'

    # 省移动
    CMCC_gd = 'CMCC_gd'
